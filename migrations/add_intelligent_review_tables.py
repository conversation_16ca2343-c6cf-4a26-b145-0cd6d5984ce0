"""
Database migration script to add tables for intelligent review system
Run this script to create the new tables for spaced repetition and performance analytics
"""

import sys
import os

# Add the parent directory to the Python path so we can import app modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from app import app, db
    from models import ReviewSchedule, PerformanceAnalytics, RecommendationHistory
    from sqlalchemy import and_
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Make sure you're running this script from the project root directory")
    print("Usage: python migrations/add_intelligent_review_tables.py --full-setup")
    sys.exit(1)


def create_intelligent_review_tables():
    """Create the new tables for the intelligent review system"""
    try:
        with app.app_context():
            print("Creating intelligent review system tables...")
            
            # Create all new tables
            db.create_all()
            
            print("✓ ReviewSchedule table created")
            print("✓ PerformanceAnalytics table created") 
            print("✓ RecommendationHistory table created")
            
            print("\nIntelligent review system tables created successfully!")
            print("\nNext steps:")
            print("1. Run the performance analytics initialization script")
            print("2. Test the new review system")
            print("3. Update the frontend to use the new recommendation API")
            
    except Exception as e:
        print(f"Error creating tables: {e}")
        sys.exit(1)


def initialize_performance_analytics():
    """Initialize performance analytics for existing users"""
    try:
        with app.app_context():
            from models import User, Submission, Question, Topic, Subject
            from routes.performance_analysis import PerformanceAnalysisEngine
            
            print("Initializing performance analytics for existing users...")
            
            # Get all users who have submissions
            users_with_submissions = db.session.query(User.id).join(
                Submission, User.id == Submission.user_id
            ).distinct().all()
            
            total_users = len(users_with_submissions)
            print(f"Found {total_users} users with submissions")
            
            for i, (user_id,) in enumerate(users_with_submissions, 1):
                print(f"Processing user {user_id} ({i}/{total_users})...")
                
                # Get all topics this user has attempted
                attempted_topics = db.session.query(Topic.id).join(
                    Question, Topic.id == Question.topic_id
                ).join(
                    Submission, Question.id == Submission.question_id
                ).filter(
                    Submission.user_id == user_id
                ).distinct().all()
                
                # Initialize analytics for each topic
                for (topic_id,) in attempted_topics:
                    try:
                        PerformanceAnalysisEngine.update_performance_analytics(user_id, topic_id=topic_id)
                    except Exception as e:
                        print(f"  Warning: Could not initialize analytics for user {user_id}, topic {topic_id}: {e}")
                
                # Get all subjects this user has attempted
                attempted_subjects = db.session.query(Subject.id).join(
                    Topic, Subject.id == Topic.subject_id
                ).join(
                    Question, Topic.id == Question.topic_id
                ).join(
                    Submission, Question.id == Submission.question_id
                ).filter(
                    Submission.user_id == user_id
                ).distinct().all()
                
                # Initialize analytics for each subject
                for (subject_id,) in attempted_subjects:
                    try:
                        PerformanceAnalysisEngine.update_performance_analytics(user_id, subject_id=subject_id)
                    except Exception as e:
                        print(f"  Warning: Could not initialize analytics for user {user_id}, subject {subject_id}: {e}")
            
            print(f"\n✓ Performance analytics initialized for {total_users} users")
            
    except Exception as e:
        print(f"Error initializing performance analytics: {e}")
        sys.exit(1)


def initialize_review_schedules():
    """Initialize review schedules for existing submissions"""
    try:
        with app.app_context():
            from models import Submission, Part
            from routes.spaced_repetition import SpacedRepetitionEngine
            
            print("Initializing review schedules for existing submissions...")
            
            # Get all submissions that don't have review schedules yet
            submissions_without_schedules = db.session.query(Submission).outerjoin(
                ReviewSchedule,
                and_(
                    ReviewSchedule.user_id == Submission.user_id,
                    ReviewSchedule.question_id == Submission.question_id,
                    ReviewSchedule.part_id == Submission.part_id
                )
            ).filter(ReviewSchedule.id.is_(None)).all()
            
            total_submissions = len(submissions_without_schedules)
            print(f"Found {total_submissions} submissions without review schedules")
            
            for i, submission in enumerate(submissions_without_schedules, 1):
                if i % 100 == 0:
                    print(f"Processing submission {i}/{total_submissions}...")
                
                try:
                    # Get the part to determine max score
                    part = Part.query.get(submission.part_id)
                    if part:
                        SpacedRepetitionEngine.update_review_schedule(
                            submission.user_id,
                            submission.question_id,
                            submission.part_id,
                            submission.score or 0,
                            part.score
                        )
                except Exception as e:
                    print(f"  Warning: Could not create review schedule for submission {submission.id}: {e}")
            
            print(f"\n✓ Review schedules initialized for {total_submissions} submissions")
            
    except Exception as e:
        print(f"Error initializing review schedules: {e}")
        sys.exit(1)


def verify_installation():
    """Verify that the intelligent review system is properly installed"""
    try:
        with app.app_context():
            print("Verifying intelligent review system installation...")
            
            # Check if tables exist and have expected structure
            tables_to_check = [
                ('review_schedule', ReviewSchedule),
                ('performance_analytics', PerformanceAnalytics),
                ('recommendation_history', RecommendationHistory)
            ]
            
            for table_name, model_class in tables_to_check:
                try:
                    count = model_class.query.count()
                    print(f"✓ {table_name}: {count} records")
                except Exception as e:
                    print(f"✗ {table_name}: Error - {e}")
                    return False
            
            # Test basic functionality
            from routes.spaced_repetition import SpacedRepetitionEngine
            from routes.performance_analysis import PerformanceAnalysisEngine
            from routes.recommendation_engine import RecommendationEngine
            
            print("\nTesting core functionality...")
            
            # Test spaced repetition
            try:
                stats = SpacedRepetitionEngine.get_review_statistics(1)  # Test with user ID 1
                print(f"✓ Spaced repetition engine working (found {stats.get('total_items', 0)} items)")
            except Exception as e:
                print(f"✗ Spaced repetition engine error: {e}")

            # Test performance analysis
            try:
                insights = PerformanceAnalysisEngine.get_learning_insights(1)  # Test with user ID 1
                print(f"✓ Performance analysis engine working (found {len(insights.get('weak_areas', []))} weak areas)")
            except Exception as e:
                print(f"✗ Performance analysis engine error: {e}")

            # Test recommendation engine
            try:
                recommendations = RecommendationEngine.generate_recommendations(1, limit=5)  # Test with user ID 1
                print(f"✓ Recommendation engine working (generated {len(recommendations)} recommendations)")
            except Exception as e:
                print(f"✗ Recommendation engine error: {e}")
            
            print("\n✓ Intelligent review system verification complete!")
            return True
            
    except Exception as e:
        print(f"Error during verification: {e}")
        return False


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Intelligent Review System Migration")
    parser.add_argument("--create-tables", action="store_true", 
                       help="Create the new database tables")
    parser.add_argument("--init-analytics", action="store_true",
                       help="Initialize performance analytics for existing users")
    parser.add_argument("--init-schedules", action="store_true",
                       help="Initialize review schedules for existing submissions")
    parser.add_argument("--verify", action="store_true",
                       help="Verify the installation")
    parser.add_argument("--full-setup", action="store_true",
                       help="Run complete setup (create tables + initialize data)")
    
    args = parser.parse_args()
    
    if args.full_setup:
        print("Running full intelligent review system setup...\n")
        create_intelligent_review_tables()
        print("\n" + "="*50 + "\n")
        initialize_performance_analytics()
        print("\n" + "="*50 + "\n")
        initialize_review_schedules()
        print("\n" + "="*50 + "\n")
        verify_installation()
        
    elif args.create_tables:
        create_intelligent_review_tables()
        
    elif args.init_analytics:
        initialize_performance_analytics()
        
    elif args.init_schedules:
        initialize_review_schedules()
        
    elif args.verify:
        verify_installation()
        
    else:
        print("Please specify an action. Use --help for options.")
        print("\nQuick start: python migrations/add_intelligent_review_tables.py --full-setup")

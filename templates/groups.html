{% extends "base.html" %}

{% block content %}
<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12 min-h-screen bg-gradient-to-b from-white to-gray-50">
    <div class="mx-auto max-w-5xl">
        <!-- Header with subtle animation -->
        <div class="text-center mb-12 fade-in-up">
            <h1 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-violet-600">Groups Leaderboard</h1>
            <p class="mt-3 text-lg text-gray-500 max-w-2xl mx-auto">Collaborate, compete, and track your collective progress</p>
        </div>

        <!-- Stats Cards - New component -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10 fade-in-up animation-delay-200">
            <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl overflow-hidden p-6 transform transition-all duration-300 hover:shadow-md hover:scale-[1.02]">
                <div class="flex items-center space-x-3">
                    <div class="p-2 bg-indigo-50 rounded-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-xs font-medium text-gray-500 uppercase">Total Groups</p>
                        <p class="text-2xl font-bold text-gray-900">{{ groups|length }}</p>
                    </div>
                </div>
            </div>
            <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl overflow-hidden p-6 transform transition-all duration-300 hover:shadow-md hover:scale-[1.02]">
                <div class="flex items-center space-x-3">
                    <div class="p-2 bg-violet-50 rounded-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-violet-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-xs font-medium text-gray-500 uppercase">Problems Solved</p>
                        <p class="text-2xl font-bold text-gray-900">{{ groups|sum(attribute='total_problems') }}</p>
                    </div>
                </div>
            </div>
            <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl overflow-hidden p-6 transform transition-all duration-300 hover:shadow-md hover:scale-[1.02]">
                <div class="flex items-center space-x-3">
                    <div class="p-2 bg-purple-50 rounded-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-xs font-medium text-gray-500 uppercase">Total Points</p>
                        <p class="text-2xl font-bold text-gray-900">{{ groups|sum(attribute='total_points')|round|int }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Bar -->
        <div class="flex flex-col sm:flex-row sm:items-center justify-between mb-6 fade-in-up animation-delay-300">
            <div class="mb-4 sm:mb-0">
                <div class="relative">
                    <input type="text" id="group-search" placeholder="Search groups..." class="w-full sm:w-64 pl-10 pr-4 py-2 border-0 rounded-lg bg-white shadow-sm ring-1 ring-gray-200 focus:ring-2 focus:ring-indigo-500 focus:outline-none transition-all duration-200">
                    <div class="absolute left-3 top-2.5 text-gray-400">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </div>
                </div>
            </div>
            <a href="{{ url_for('create_group') }}" id="create-group-button"
               class="inline-flex items-center justify-center rounded-lg bg-gradient-to-r from-indigo-600 to-violet-600 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:from-indigo-500 hover:to-violet-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 transition-all duration-200 transform hover:scale-105">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                Create Group
            </a>
        </div>

        <!-- Groups Leaderboard - Redesigned with cards -->
        <div class="space-y-4 fade-in-up animation-delay-400" id="groups-container">
            {% for group_stats in groups %}
            <div class="group-card bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl overflow-hidden transition-all duration-300 hover:shadow-md transform hover:translate-y-[-2px] {% if loop.index == 1 %}border-l-4 border-yellow-400{% elif loop.index == 2 %}border-l-4 border-gray-400{% elif loop.index == 3 %}border-l-4 border-orange-400{% endif %}"
                 data-problems="{{ group_stats.total_problems }}"
                 data-points="{{ group_stats.total_points|round|int }}"
                 data-members="{{ group_stats.member_count }}"
                 data-name="{{ group_stats.group.name|lower }}">
                <div class="p-6">
                    <div class="flex items-start">
                        <!-- Rank -->
                        <div class="flex-shrink-0 w-12 h-12 flex items-center justify-center {% if loop.index == 1 %}bg-yellow-50{% elif loop.index == 2 %}bg-gray-50{% elif loop.index == 3 %}bg-orange-50{% else %}bg-indigo-50{% endif %} rounded-lg">
                            {% if loop.index == 1 %}
                                <span class="text-yellow-500 text-xl font-bold flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                                    </svg>
                                </span>
                            {% elif loop.index == 2 %}
                                <span class="text-gray-500 text-xl font-bold flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                    </svg>
                                </span>
                            {% elif loop.index == 3 %}
                                <span class="text-orange-500 text-xl font-bold flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                                    </svg>
                                </span>
                            {% else %}
                                <span class="text-indigo-500 text-lg font-bold">{{ loop.index }}</span>
                            {% endif %}
                        </div>

                        <!-- Group Info -->
                        <div class="ml-5 flex-1">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-gray-900">{{ group_stats.group.name }}</h3>
                                <div>
                                    {% if group_stats.is_member %}
                                        <a href="{{ url_for('group_details', group_id=group_stats.group.id) }}"
                                           class="inline-flex items-center rounded-md bg-indigo-50 px-3 py-1.5 text-sm font-medium text-indigo-700 hover:bg-indigo-100 transition-colors duration-200">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                            </svg>
                                            View Group
                                        </a>
                                    {% else %}
                                        <form action="{{ url_for('join_group', group_id=group_stats.group.id) }}" method="POST" class="inline">
                                            <button type="submit"
                                                   {% if loop.first %}id="first-join-group-button"{% endif %}
                                                   class="inline-flex items-center rounded-md bg-green-50 px-3 py-1.5 text-sm font-medium text-green-700 hover:bg-green-100 transition-colors duration-200">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                                                </svg>
                                                Join Group
                                            </button>
                                        </form>
                                    {% endif %}
                                </div>
                            </div>

                            {% if group_stats.group.description %}
                            <p class="mt-1 text-sm text-gray-500">{{ group_stats.group.description }}</p>
                            {% endif %}

                            <!-- Stats -->
                            <div class="mt-4 grid grid-cols-3 gap-4">
                                <div class="flex flex-col">
                                    <span class="text-xs text-gray-500">Problems Solved</span>
                                    <span class="text-lg font-semibold text-indigo-600">{{ group_stats.total_problems }}</span>
                                </div>
                                <div class="flex flex-col">
                                    <span class="text-xs text-gray-500">Total Points</span>
                                    <span class="text-lg font-semibold text-violet-600">{{ group_stats.total_points|round|int }}</span>
                                </div>
                                <div class="flex flex-col">
                                    <span class="text-xs text-gray-500">Members</span>
                                    <span class="text-lg font-semibold text-purple-600">{{ group_stats.member_count }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}

            <!-- Empty state -->
            {% if not groups %}
            <div class="text-center py-16 px-4 bg-white shadow-sm ring-1 ring-gray-900/5 rounded-xl overflow-hidden">
                <svg class="mx-auto h-24 w-24 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <h3 class="mt-4 text-lg font-medium text-gray-900">No groups yet</h3>
                <p class="mt-2 text-sm text-gray-500 max-w-md mx-auto">Create a group to collaborate with others and track your collective progress.</p>
                <div class="mt-6">
                    <a href="{{ url_for('create_group') }}" class="inline-flex items-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-semibold text-white shadow-sm transition-all duration-200 hover:bg-indigo-500 hover:shadow-md focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                        Create Your First Group
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
    /* Fade in up animation */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .fade-in-up {
        animation: fadeInUp 0.8s ease-out forwards;
        opacity: 0;
    }

    .animation-delay-200 {
        animation-delay: 0.2s;
    }

    .animation-delay-300 {
        animation-delay: 0.3s;
    }

    .animation-delay-400 {
        animation-delay: 0.4s;
    }

    /* Hover scaling animation for cards */
    .card-zoom-hover {
        transition: transform 0.3s ease-in-out;
    }

    .card-zoom-hover:hover {
        transform: scale(1.02);
    }

    /* Subtle background pattern */
    .bg-pattern {
        background-color: #ffffff;
        background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Search functionality
        const searchInput = document.getElementById('group-search');
        const groupCards = document.querySelectorAll('.group-card');

        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase().trim();

            groupCards.forEach(card => {
                const groupName = card.getAttribute('data-name');

                if (groupName.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });

        // Staggered animation for cards
        groupCards.forEach((card, index) => {
            setTimeout(() => {
                card.classList.add('animate-fade-in');
            }, 100 + (index * 50));
        });
    });

    // Shepherd.js tour for the groups page
    document.addEventListener('DOMContentLoaded', function() {
        const isLoggedIn = {{ session.get('user_id') is not none | tojson }};
        const firstJoinButton = document.getElementById('first-join-group-button');
        const createGroupButton = document.getElementById('create-group-button');
        const groupsHeader = document.querySelector('.text-center.mb-12.fade-in-up'); // Main header of the groups page

        let isContinuation = false;
        if (localStorage.getItem('startGroupsTourNext') === 'true') {
            localStorage.removeItem('startGroupsTourNext'); // Consume the signal
            localStorage.removeItem('groupsShepherdTourCompleted'); // Ensure this tour runs
            isContinuation = true;
        }

        const tourCompleted = localStorage.getItem('groupsShepherdTourCompleted');

        // Start tour if logged in AND (it's a continuation OR tour hasn't been completed)
        // AND there's a header to attach the first step to.
        if (isLoggedIn && (isContinuation || !tourCompleted) && groupsHeader) {
            const tour = new Shepherd.Tour({
                useModalOverlay: true,
                defaultStepOptions: {
                    classes: 'bg-white rounded-lg shadow-xl border border-gray-300 text-gray-700', // Updated classes
                    scrollTo: { behavior: 'smooth', block: 'center' }
                }
            });

            // Step 1: General intro to groups page
            tour.addStep({
                id: 'groups-page-intro',
                text: 'Welcome to the Groups page! Here you can find groups to join, create your own, or see groups you are already a member of.',
                attachTo: { element: groupsHeader, on: 'bottom' },
                buttons: [
                    // This button will be updated if no other steps are added
                    {
                        action() { return this.next(); },
                        text: 'Next',
                        classes: 'px-4 py-2 bg-indigo-600 text-white text-sm font-semibold rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2'
                    },
                    {
                        action() {
                            localStorage.setItem('groupsShepherdTourCompleted', 'true');
                            localStorage.setItem('problemsetsShepherdTourCompleted', 'true');
                            return this.complete();
                        },
                        text: 'Skip Tour',
                        classes: 'px-4 py-2 bg-transparent text-gray-600 text-sm font-semibold rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 ml-2'
                    }
                ]
            });

            let subsequentStepAdded = false;

            // Step 2: Point to "Join Group" button if available
            if (firstJoinButton) {
                tour.addStep({
                    id: 'join-specific-group',
                    text: 'Try joining this top group! Clicking "Join Group" here will let you become a member and can unlock access to public resources and collaborative problem sets.',
                    attachTo: { element: firstJoinButton, on: 'bottom' },
                    buttons: [
                        {
                            action() { return this.back(); },
                            text: 'Back',
                            classes: 'px-4 py-2 bg-gray-200 text-gray-700 text-sm font-semibold rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2'
                        },
                        {
                            action() {
                                localStorage.setItem('groupsShepherdTourCompleted', 'true');
                                localStorage.setItem('startProblemsetsTourNext', 'true');
                                window.location.href = "{{ url_for('list_problemsets') }}";
                                return this.complete();
                            },
                            text: 'Go to Problem Sets & Continue Tour',
                            classes: 'px-4 py-2 bg-indigo-600 text-white text-sm font-semibold rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2'
                        },
                        {
                            action() {
                                localStorage.setItem('groupsShepherdTourCompleted', 'true');
                                localStorage.setItem('problemsetsShepherdTourCompleted', 'true');
                                return this.complete();
                            },
                            text: 'Skip Tour',
                            classes: 'px-4 py-2 bg-transparent text-gray-600 text-sm font-semibold rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 ml-2'
                        }
                    ]
                });
                subsequentStepAdded = true;
            }
            // Step 3: Else, if no "Join Group" button, point to "Create Group" button if it exists
            else if (createGroupButton) {
                tour.addStep({
                    id: 'create-new-group-option',
                    text: 'You can create a new group using this button if you wish.',
                    attachTo: { element: createGroupButton, on: 'bottom' },
                    buttons: [
                        {
                            action() { return this.back(); },
                            text: 'Back',
                            classes: 'px-4 py-2 bg-gray-200 text-gray-700 text-sm font-semibold rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2'
                        },
                        {
                            action() {
                                localStorage.setItem('groupsShepherdTourCompleted', 'true');
                                localStorage.setItem('startProblemsetsTourNext', 'true');
                                window.location.href = "{{ url_for('list_problemsets') }}";
                                return this.complete();
                            },
                            text: 'Go to Problem Sets & Continue Tour',
                            classes: 'px-4 py-2 bg-indigo-600 text-white text-sm font-semibold rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2'
                        },
                        {
                            action() {
                                localStorage.setItem('groupsShepherdTourCompleted', 'true');
                                localStorage.setItem('problemsetsShepherdTourCompleted', 'true');
                                return this.complete();
                            },
                            text: 'Skip Tour',
                            classes: 'px-4 py-2 bg-transparent text-gray-600 text-sm font-semibold rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 ml-2'
                        }
                    ]
                });
                subsequentStepAdded = true;
            }

            // If only the intro step was added, modify its button to be "Finish Tour"
            if (!subsequentStepAdded) {
                const introStep = tour.getById('groups-page-intro');
                if (introStep) {
                    introStep.updateOptions({
                        buttons: [
                            {
                                action() {
                                    localStorage.setItem('groupsShepherdTourCompleted', 'true');
                                    localStorage.setItem('startProblemsetsTourNext', 'true');
                                    window.location.href = "{{ url_for('list_problemsets') }}";
                                    return this.complete();
                                },
                                text: 'Go to Problem Sets & Continue Tour',
                                classes: 'px-4 py-2 bg-indigo-600 text-white text-sm font-semibold rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2'
                            },
                            {
                                action() {
                                    localStorage.setItem('groupsShepherdTourCompleted', 'true');
                                    localStorage.setItem('problemsetsShepherdTourCompleted', 'true');
                                    return this.complete();
                                },
                                text: 'Skip Tour',
                                classes: 'px-4 py-2 bg-transparent text-gray-600 text-sm font-semibold rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 ml-2'
                            }
                        ]
                    });
                }
            }

            tour.on('complete', function() {
                localStorage.setItem('groupsShepherdTourCompleted', 'true');
            });
            tour.on('cancel', function() {
                localStorage.setItem('groupsShepherdTourCompleted', 'true'); // Also mark as completed on cancel
            });

            tour.start();
        } else if (isLoggedIn && isContinuation && !groupsHeader) {
            // If it's a continuation but the main header isn't found,
            // we can't even start the basic groups tour. Mark as completed.
            localStorage.setItem('groupsShepherdTourCompleted', 'true');
        }
    });
</script>
{% endblock %}

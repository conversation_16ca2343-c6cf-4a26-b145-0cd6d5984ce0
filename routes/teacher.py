from flask import render_template, request, jsonify, session, flash, redirect, url_for
from sqlalchemy import func, desc, asc
from models import db, User, Subject, Topic, Question, Part, Submission
from .utils import admin_required, error_logger, app_logger
import json


def register_teacher_routes(app, db, session):
    """Register teacher dashboard routes"""

    @app.route("/teacher")
    @admin_required
    def teacher_dashboard():
        """Teacher dashboard with submission analytics"""
        try:
            # Get all subjects for filtering
            subjects = Subject.query.order_by(Subject.name).all()
            
            # Get initial data (all submissions)
            analytics_data = get_submission_analytics()
            
            return render_template('teacher_dashboard.html',
                                 title="Teacher Dashboard",
                                 subjects=subjects,
                                 analytics_data=analytics_data)
        except Exception as e:
            error_logger.exception("Error loading teacher dashboard")
            flash("Error loading teacher dashboard.", "error")
            return redirect(url_for('index'))

    @app.route("/api/teacher/analytics")
    @admin_required
    def get_teacher_analytics():
        """API endpoint for filtered and sorted analytics data"""
        try:
            subject_id = request.args.get('subject_id', type=int)
            topic_id = request.args.get('topic_id', type=int)
            sort_by = request.args.get('sort_by', 'avg_score_asc')
            view_mode = request.args.get('view_mode', 'users')  # 'users' or 'questions'

            if view_mode == 'questions':
                # Return question-level analytics when in questions mode
                question_data = get_question_analytics(subject_id, topic_id, sort_by)
                return jsonify({
                    'status': 'success',
                    'data': {
                        'view_mode': 'questions',
                        'question_analytics': question_data,
                        'summary': {
                            'total_questions': len(question_data),
                            'avg_difficulty': calculate_avg_difficulty(question_data),
                            'total_attempts': sum(q['total_attempts'] for q in question_data)
                        }
                    }
                })
            else:
                # Return user-level analytics (default behavior)
                analytics_data = get_submission_analytics(
                    subject_id=subject_id,
                    topic_id=topic_id,
                    sort_by=sort_by
                )
                analytics_data['view_mode'] = 'users'
                return jsonify({
                    'status': 'success',
                    'data': analytics_data
                })

        except Exception as e:
            error_logger.exception("Error getting teacher analytics")
            return jsonify({
                'status': 'error',
                'message': 'Failed to load analytics data'
            }), 500

    @app.route("/api/teacher/topics/<int:subject_id>")
    @admin_required
    def get_topics_for_subject(subject_id):
        """Get topics for a specific subject"""
        try:
            topics = Topic.query.filter_by(subject_id=subject_id).order_by(Topic.name).all()
            topics_data = [{'id': topic.id, 'name': topic.name} for topic in topics]
            
            return jsonify({
                'status': 'success',
                'topics': topics_data
            })
        except Exception as e:
            error_logger.exception(f"Error getting topics for subject {subject_id}")
            return jsonify({
                'status': 'error',
                'message': 'Failed to load topics'
            }), 500

    @app.route("/api/teacher/question-analytics")
    @admin_required
    def get_question_analytics_api():
        """API endpoint for question-level analytics"""
        try:
            subject_id = request.args.get('subject_id', type=int)
            topic_id = request.args.get('topic_id', type=int)

            question_data = get_question_analytics(subject_id, topic_id)

            return jsonify({
                'status': 'success',
                'data': question_data
            })

        except Exception as e:
            error_logger.exception("Error loading question analytics")
            return jsonify({
                'status': 'error',
                'message': 'Failed to load question analytics'
            }), 500


def get_submission_analytics(subject_id=None, topic_id=None, sort_by='avg_score_asc'):
    """
    Get submission analytics data with optional filtering and sorting

    Args:
        subject_id: Filter by specific subject
        topic_id: Filter by specific topic
        sort_by: Sort criteria ('avg_score_asc', 'avg_score_desc', 'submissions_asc', 'submissions_desc')

    Returns:
        Dictionary containing analytics data
    """
    try:
        # Base query for user submission statistics
        query = db.session.query(
            User.id,
            User.username,
            User.email,
            User.grade_level,
            func.count(Submission.id).label('total_submissions'),
            func.avg(Submission.score).label('avg_score'),
            func.count(func.distinct(Submission.question_id)).label('unique_questions'),
            func.max(Submission.timestamp).label('last_submission_date')
        ).join(
            Submission, User.id == Submission.user_id
        ).join(
            Part, Submission.part_id == Part.id
        ).join(
            Question, Part.question_id == Question.id
        ).join(
            Topic, Question.topic_id == Topic.id
        ).join(
            Subject, Topic.subject_id == Subject.id
        )

        # Apply filters
        if subject_id:
            query = query.filter(Subject.id == subject_id)
        if topic_id:
            query = query.filter(Topic.id == topic_id)

        # Group by user
        query = query.group_by(User.id, User.username, User.email, User.grade_level)

        # Apply sorting
        if sort_by == 'avg_score_asc':
            query = query.order_by(asc('avg_score'))
        elif sort_by == 'avg_score_desc':
            query = query.order_by(desc('avg_score'))
        elif sort_by == 'submissions_asc':
            query = query.order_by(asc('total_submissions'))
        elif sort_by == 'submissions_desc':
            query = query.order_by(desc('total_submissions'))

        results = query.all()

        # Format the data
        user_analytics = []
        for result in results:
            # Calculate performance metrics
            avg_score = float(result.avg_score or 0)
            performance_level = 'Excellent' if avg_score >= 80 else 'Good' if avg_score >= 60 else 'Needs Improvement'

            user_analytics.append({
                'user_id': result.id,
                'username': result.username,
                'email': result.email,
                'grade_level': result.grade_level or 'Not specified',
                'total_submissions': result.total_submissions,
                'avg_score': round(avg_score, 2),
                'unique_questions': result.unique_questions,
                'last_submission_date': result.last_submission_date.strftime('%Y-%m-%d %H:%M') if result.last_submission_date else 'Never',
                'performance_level': performance_level,
                'submission_frequency': round(result.total_submissions / result.unique_questions, 2) if result.unique_questions > 0 else 0
            })

        # Get overall statistics
        total_users = len(user_analytics)
        total_submissions = sum(user['total_submissions'] for user in user_analytics)
        overall_avg_score = sum(user['avg_score'] for user in user_analytics) / total_users if total_users > 0 else 0

        # Calculate performance distribution
        excellent_count = sum(1 for user in user_analytics if user['avg_score'] >= 80)
        good_count = sum(1 for user in user_analytics if 60 <= user['avg_score'] < 80)
        needs_improvement_count = sum(1 for user in user_analytics if user['avg_score'] < 60)

        return {
            'user_analytics': user_analytics,
            'summary': {
                'total_users': total_users,
                'total_submissions': total_submissions,
                'overall_avg_score': round(overall_avg_score, 2),
                'performance_distribution': {
                    'excellent': excellent_count,
                    'good': good_count,
                    'needs_improvement': needs_improvement_count
                }
            }
        }

    except Exception as e:
        error_logger.exception("Error in get_submission_analytics")
        return {
            'user_analytics': [],
            'summary': {
                'total_users': 0,
                'total_submissions': 0,
                'overall_avg_score': 0,
                'performance_distribution': {
                    'excellent': 0,
                    'good': 0,
                    'needs_improvement': 0
                }
            }
        }


def calculate_avg_difficulty(question_data):
    """Calculate average difficulty based on average scores"""
    if not question_data:
        return 0
    total_avg = sum(q['avg_score'] for q in question_data)
    return round(total_avg / len(question_data), 2)


def get_question_analytics(subject_id=None, topic_id=None, sort_by='avg_score_asc'):
    """
    Get question-level analytics showing which questions are most/least attempted
    and their average scores

    Args:
        subject_id: Filter by specific subject
        topic_id: Filter by specific topic
        sort_by: Sort criteria for questions

    Returns:
        List of question analytics
    """
    try:
        # Query for question-level statistics
        # We need to aggregate scores at the question level, not part level
        # First, get all submissions with their question info
        subquery = db.session.query(
            Question.id.label('question_id'),
            Question.title,
            Question.source,
            Topic.name.label('topic_name'),
            Subject.name.label('subject_name'),
            Submission.user_id,
            Submission.score,
            Submission.id.label('submission_id')
        ).join(
            Topic, Question.topic_id == Topic.id
        ).join(
            Subject, Topic.subject_id == Subject.id
        ).join(
            Part, Question.id == Part.question_id
        ).join(
            Submission, Part.id == Submission.part_id
        )

        # Apply filters to subquery
        if subject_id:
            subquery = subquery.filter(Subject.id == subject_id)
        if topic_id:
            subquery = subquery.filter(Topic.id == topic_id)

        subquery = subquery.subquery()

        # Now aggregate by question
        query = db.session.query(
            subquery.c.question_id.label('id'),
            subquery.c.title,
            subquery.c.source,
            subquery.c.topic_name,
            subquery.c.subject_name,
            func.count(subquery.c.submission_id).label('total_attempts'),
            func.count(func.distinct(subquery.c.user_id)).label('unique_students'),
            func.avg(subquery.c.score).label('avg_score'),
            func.min(subquery.c.score).label('min_score'),
            func.max(subquery.c.score).label('max_score')
        ).group_by(
            subquery.c.question_id,
            subquery.c.title,
            subquery.c.source,
            subquery.c.topic_name,
            subquery.c.subject_name
        )



        # Apply sorting
        if sort_by == 'avg_score_asc':
            query = query.order_by(asc('avg_score'))
        elif sort_by == 'avg_score_desc':
            query = query.order_by(desc('avg_score'))
        elif sort_by == 'submissions_asc':
            query = query.order_by(asc('total_attempts'))
        elif sort_by == 'submissions_desc':
            query = query.order_by(desc('total_attempts'))
        else:
            # Default: order by total attempts descending
            query = query.order_by(desc('total_attempts'))

        results = query.all()

        question_analytics = []
        for result in results:
            avg_score = float(result.avg_score or 0) * 100  # Convert to percentage
            difficulty_level = 'Easy' if avg_score >= 80 else 'Medium' if avg_score >= 60 else 'Hard'

            question_analytics.append({
                'question_id': result.id,
                'title': result.title or f"Question {result.id}",
                'source': result.source or 'Unknown',
                'topic_name': result.topic_name,
                'subject_name': result.subject_name,
                'total_attempts': result.total_attempts,
                'unique_students': result.unique_students,
                'avg_score': round(avg_score, 2),
                'min_score': float(result.min_score or 0) * 100,  # Convert to percentage
                'max_score': float(result.max_score or 0) * 100,  # Convert to percentage
                'difficulty_level': difficulty_level
            })

        return question_analytics

    except Exception as e:
        error_logger.exception("Error in get_question_analytics")
        return []
